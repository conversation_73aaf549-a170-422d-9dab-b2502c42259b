local Config = require('config')
local createdBlips = {}
local createdRadiusBlips = {}

local function createRegularBlips()
    if not Config.Blips.enabled then return end
    
    for i, blipData in ipairs(Config.Blips.regularBlips) do
        if blipData.enabled then
            local blip = AddBlipForCoord(blipData.coords.x, blipData.coords.y, blipData.coords.z)
            
            SetBlipSprite(blip, blipData.sprite)
            SetBlipColour(blip, blipData.color)
            SetBlipScale(blip, blipData.scale)
            SetBlipAsShortRange(blip, blipData.shortRange)
            
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(blipData.name)
            EndTextCommandSetBlipName(blip)
            
            createdBlips[i] = blip
        end
    end
end

local function createRadiusBlips()
    if not Config.Blips.enabled then return end
    
    for i, radiusData in ipairs(Config.Blips.radiusBlips) do
        if radiusData.enabled then
            local blip = AddBlipForRadius(radiusData.coords.x, radiusData.coords.y, radiusData.coords.z, radiusData.radius)
            
            SetBlipColour(blip, radiusData.color)
            SetBlipAlpha(blip, radiusData.alpha)
            
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(radiusData.name)
            EndTextCommandSetBlipName(blip)
            
            createdRadiusBlips[i] = blip
        end
    end
end

local function removeAllBlips()
    for _, blip in pairs(createdBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    createdBlips = {}
    
    for _, blip in pairs(createdRadiusBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    createdRadiusBlips = {}
end

local function refreshBlips()
    removeAllBlips()
    createRegularBlips()
    createRadiusBlips()
end

AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    if Config.Blips.enabled then
        createRegularBlips()
        createRadiusBlips()
        print("[BLIPS] Blip system loaded successfully")
    else
        print("[BLIPS] Blip system is disabled in config")
    end
end)

AddEventHandler('onClientResourceStop', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    removeAllBlips()
end)

RegisterNetEvent('gmaddon:blips:refresh', function()
    refreshBlips()
end)

RegisterNetEvent('gmaddon:blips:toggle', function(enabled)
    if enabled then
        createRegularBlips()
        createRadiusBlips()
    else
        removeAllBlips()
    end
end)

RegisterCommand('refreshblips', function()
    refreshBlips()
    TriggerEvent('chat:addMessage', {
        color = { 0, 255, 0 },
        args = { "[BLIPS]", "All blips have been refreshed!" }
    })
end, false)

RegisterCommand('toggleblips', function()
    Config.Blips.enabled = not Config.Blips.enabled
    
    if Config.Blips.enabled then
        createRegularBlips()
        createRadiusBlips()
        TriggerEvent('chat:addMessage', {
            color = { 0, 255, 0 },
            args = { "[BLIPS]", "Blips enabled!" }
        })
    else
        removeAllBlips()
        TriggerEvent('chat:addMessage', {
            color = { 255, 165, 0 },
            args = { "[BLIPS]", "Blips disabled!" }
        })
    end
end, false)
