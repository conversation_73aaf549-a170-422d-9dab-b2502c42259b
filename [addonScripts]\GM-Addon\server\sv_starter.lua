ESX = exports["es_extended"]:getSharedObject()

local Config = require('config')

ESX.RegisterUsableItem('starter_packf', function(source, item)
    TriggerClientEvent('gmaddon:starterpack:progress', source)
end)

RegisterNetEvent('gmaddon:starterpack:reward', function()
    local src = source
    local hasItem = exports.ox_inventory:GetItem(src, 'starter_packf')
    local itemCount = (hasItem and (hasItem.count or hasItem.amount)) or 0
    if itemCount > 0 then
        for _, v in ipairs(Config.Reward) do
            exports.ox_inventory:AddItem(src, v.name, v.count)
        end
        exports.ox_inventory:RemoveItem(src, 'starter_packf', 1)
        TriggerClientEvent('ox_lib:notify', src, {type = 'success', description = 'You received your starter pack!'})
    else
        --print('Item Returning Nill or count is 0')
        DropPlayer(src, 'Cheating detected: Attempted to claim starter pack.')
    end
end)
