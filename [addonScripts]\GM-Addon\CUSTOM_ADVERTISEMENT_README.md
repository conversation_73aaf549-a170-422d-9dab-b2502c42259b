# Advertisement System with Custom UI

## Overview
This system allows players to post city-wide advertisements using the `/ad` command. The system integrates with ESX banking, includes cooldown management, and displays beautiful custom notifications to all players.

## Features
- **Custom Notification UI**: Beautiful animated notifications with gradient backgrounds
- **Bank Integration**: Automatically deducts money from player's bank account
- **Cooldown System**: Prevents spam with configurable cooldown periods
- **Message Validation**: Enforces minimum and maximum message lengths
- **Sound Effects**: Plays notification sounds when ads are displayed
- **Mobile Responsive**: Works on different screen sizes
- **Configurable**: All settings can be adjusted in `config.lua`

## Usage
Players can use the command: `/ad [message]`

Example: `/ad Selling rare car at Legion Square! Contact me for details.`

## Custom UI Features
- **Gradient Background**: Beautiful purple gradient with glassmorphism effect
- **Smooth Animations**: Slide-in animations with bounce effects
- **Progress Bar**: Visual countdown showing remaining display time
- **Close Button**: Players can manually close notifications
- **Pulse Animation**: Subtle glow effect to draw attention
- **Auto-positioning**: Notifications stack properly on top-right corner

## Configuration
All settings are in `config.lua` under `Config.Advertisement`:

- `enabled`: Enable/disable the system
- `cost`: Cost per advertisement (default: $500)
- `cooldown`: Cooldown in seconds (default: 300 = 5 minutes)
- `maxLength`: Maximum message length (default: 150 characters)
- `minLength`: Minimum message length (default: 10 characters)
- `txadmin.enabled`: Enable custom notifications
- `txadmin.title`: Title for notifications
- `txadmin.duration`: How long notifications display (8 seconds)
- `fallback_to_chat`: Also send to chat as backup

## Testing the System

### Prerequisites
1. Ensure ESX framework is running
2. Player must have sufficient bank money ($500 default)
3. ox_lib must be loaded for inventory/banking

### Test Commands
1. **Basic Advertisement**: `/ad This is a test advertisement`
2. **Test Notification**: `/testadnotif` (debug command)

### Test Cases
1. **Successful Ad**: Money deducted, custom notification appears, chat backup sent
2. **Insufficient Funds**: Error notification, no money deducted
3. **Cooldown Test**: Try posting two ads quickly, second should be blocked
4. **Message Validation**: Test with very short or very long messages

## Files Structure
```
GM-Addon/
├── html/
│   └── advertisement.html          # Custom notification UI
├── client/
│   └── cl_advertisement.lua        # Client-side handlers
├── server/
│   └── sv_advertisement.lua        # Server-side logic
├── config.lua                     # Configuration settings
└── fxmanifest.lua                 # Resource manifest
```

## How It Works
1. Player uses `/ad [message]` command
2. Server validates message and checks funds/cooldown
3. Money is deducted from bank account
4. Server triggers client event for all players
5. Custom HTML notification appears with animation
6. Notification auto-closes after configured duration
7. Chat backup message is also sent (if enabled)

## Customization
You can customize the notification appearance by editing `html/advertisement.html`:
- Change colors in the CSS gradient
- Modify animation timings
- Adjust notification size and position
- Change fonts and styling

## Troubleshooting
- If notifications don't appear, check browser console (F12)
- Verify fxmanifest.lua includes the HTML file
- Check server console for error messages
- Ensure ESX banking system is working properly
- Test with `/testadnotif` command first

## Performance
- Notifications are lightweight HTML/CSS/JS
- Automatic cleanup prevents memory leaks
- Efficient event handling
- Mobile-optimized for different screen sizes
