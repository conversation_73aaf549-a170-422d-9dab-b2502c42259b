<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', sans-serif;
            background: transparent;
            overflow: hidden;
        }

        .notification-container {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            width: 400px;
            max-width: 25vw;
            min-width: 350px;
            z-index: 9999;
            pointer-events: none;
        }

        .advertisement-notification {
            background: linear-gradient(135deg, #111111 0%, rgba(87, 86, 79, 0.65) 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 10px;
            box-shadow: none;
            border: none;
            backdrop-filter: none;
            transform: translateX(-450px);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
        }

        .advertisement-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .advertisement-notification.hide {
            transform: translateX(-450px);
            opacity: 0;
        }

        .notification-header {
            margin-bottom: 12px;
        }

        .notification-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .notification-content {
            color: #ffffff;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 12px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .notification-author {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-weight: 500;
            text-align: right;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 8px;
        }

        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 0 0 12px 12px;
            transition: width linear;
        }



        /* 4K and high resolution support */
        @media (min-width: 2560px) {
            .notification-container {
                width: 500px;
                max-width: 20vw;
                min-width: 450px;
                left: 30px;
            }

            .notification-title {
                font-size: 18px;
            }

            .notification-content {
                font-size: 16px;
            }

            .notification-author {
                font-size: 14px;
            }
        }

        /* Large screens (1440p) */
        @media (min-width: 1920px) and (max-width: 2559px) {
            .notification-container {
                width: 450px;
                max-width: 22vw;
                left: 25px;
            }
        }

        /* Mobile and small screens */
        @media (max-width: 768px) {
            .notification-container {
                width: calc(100vw - 40px);
                max-width: none;
                min-width: 280px;
                left: 20px;
                right: 20px;
                top: 20px;
                transform: none;
            }

            .advertisement-notification {
                transform: translateY(-100px);
                padding: 15px;
            }

            .advertisement-notification.show {
                transform: translateY(0);
            }

            .advertisement-notification.hide {
                transform: translateY(-100px);
            }

            .notification-title {
                font-size: 14px;
            }

            .notification-content {
                font-size: 13px;
            }

            .notification-author {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="notification-container" id="notificationContainer"></div>

    <script>
        let notificationId = 0;

        function createNotification(data) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            const currentId = ++notificationId;

            notification.className = 'advertisement-notification';
            notification.id = `notification-${currentId}`;
            notification.innerHTML = `
                <div class="notification-header">
                    <div class="notification-title">${data.title || 'City Advertisement'}</div>
                </div>
                <div class="notification-content">${data.message}</div>
                <div class="notification-author">— ${data.author}</div>
                <div class="notification-progress" id="progress-${currentId}"></div>
            `;

            container.appendChild(notification);
            setTimeout(() => notification.classList.add('show'), 100);

            const duration = data.duration || 15000;
            const progressBar = document.getElementById(`progress-${currentId}`);

            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.style.transition = 'none';
                setTimeout(() => {
                    progressBar.style.transition = `width ${duration}ms linear`;
                    progressBar.style.width = '0%';
                }, 50);
            }

            setTimeout(() => {
                notification.classList.add('hide');
                setTimeout(() => notification.remove(), 500);
            }, duration);
        }

        window.addEventListener('message', function(event) {
            if (event.data.type === 'showAdvertisement') {
                createNotification(event.data);
            }
        });


    </script>
</body>
</html>
