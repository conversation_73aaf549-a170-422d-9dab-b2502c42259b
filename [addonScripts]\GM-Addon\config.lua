Config = {}

Config.Reward = {
    { name = 'weapon_pepperspray', count = 1 },
    { name = 'dirtymoney', count = 5000 },
    { name = 'money', count = 5000 },
    { name = 'WEAPON_HATCHET', count = 1 },

}

-- Advertisement System Configuration
Config.Advertisement = {
    enabled = true,                    -- Enable/disable the advertisement system
    cost = 500,                       -- Cost per advertisement in bank money
    cooldown = 300,                   -- Cooldown in seconds (5 minutes)
    maxLength = 150,                  -- Maximum message length
    minLength = 10,                   -- Minimum message length

    -- Custom Notification System
    txadmin = {
        enabled = true,               -- Enable custom notifications
        title = "📢 City Advertisement", -- Title for the notification
        duration = 15000,              -- Duration in milliseconds (8 seconds)
        type = "info",                -- Notification type: info, success, warning, error
        fallback_to_chat = true,      -- Also send to chat as backup
        fallback_to_notify = false    -- Use ox_lib notifications as backup (disabled by default)
    },

    -- Notification settings for players
    notifications = {
        success = "Advertisement posted successfully! Cost: $%s",
        insufficient_funds = "Insufficient funds! You need $%s to post an advertisement.",
        cooldown = "You must wait %s more seconds before posting another advertisement.",
        too_short = "Advertisement message is too short! Minimum %s characters required.",
        too_long = "Advertisement message is too long! Maximum %s characters allowed.",
        invalid_message = "Please provide a valid advertisement message."
    }
}

return Config
