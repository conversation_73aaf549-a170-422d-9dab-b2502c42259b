Config = {}

-- Starter Pack
Config.Reward = {
    { name = 'weapon_pepperspray', count = 1 },
    { name = 'dirtymoney', count = 5000 },
    { name = 'money', count = 5000 },
    { name = 'WEAPON_HATCHET', count = 1 },

}

-- Advertisement System 
Config.Advertisement = {
    enabled = true,                   
    cost = 2500,                       
    cooldown = 300,                   -- (5 minutes)
    maxLength = 250,                
    minLength = 10,                   

    jobRestricted = true,            
    allowedJobs = {                   
        'catcafe',  
        'hotbox',
        'mommymm',
        'vanillaunicorn',
        'venguardautos'
    },

    notification = {
        enabled = true,              
        title = "📢 Advertisement",
        duration = 15000,             -- (15 seconds)
    },

    notifications = {
        success = "Advertisement posted successfully! Cost: $%s",
        insufficient_funds = "Insufficient funds! You need $%s to post an advertisement.",
        cooldown = "You must wait %s more seconds before posting another advertisement.",
        too_short = "Advertisement message is too short! Minimum %s characters required.",
        too_long = "Advertisement message is too long! Maximum %s characters allowed.",
        invalid_message = "Please provide a valid advertisement message.",
        job_restricted = "You do not have permission to use advertisements. Only certain jobs can post ads."
    }
}

-- Blip System Configuration
Config.Blips = {
    enabled = true,                   -- Enable/disable the entire blip system

    -- Regular Blips (markers on map)
    regularBlips = {
        {
            name = "Police Station",
            coords = vector3(428.9, -984.5, 30.7),
            sprite = 60,              -- Blip sprite ID
            color = 29,               -- Blip color ID
            scale = 0.8,              -- Blip size
            shortRange = true,        -- Only show when close
            enabled = true            -- Enable/disable this specific blip
        },
        {
            name = "Hospital",
            coords = vector3(307.7, -1433.4, 29.9),
            sprite = 61,
            color = 1,
            scale = 0.8,
            shortRange = true,
            enabled = true
        },
        {
            name = "Mechanic Shop",
            coords = vector3(-347.3, -133.6, 39.0),
            sprite = 446,
            color = 5,
            scale = 0.7,
            shortRange = true,
            enabled = true
        },
        {
            name = "Bank",
            coords = vector3(150.3, -1040.2, 29.4),
            sprite = 108,
            color = 2,
            scale = 0.8,
            shortRange = false,
            enabled = true
        },
        {
            name = "Ammunation",
            coords = vector3(22.0, -1107.3, 29.8),
            sprite = 110,
            color = 1,
            scale = 0.7,
            shortRange = true,
            enabled = true
        }
    },

    -- Radius Blips (circular areas on map)
    radiusBlips = {
        {
            name = "Safe Zone - City Center",
            coords = vector3(-265.0, -880.0, 31.0),
            radius = 150.0,           -- Radius in game units
            color = 2,                -- Blip color ID
            alpha = 100,              -- Transparency (0-255)
            enabled = true            -- Enable/disable this radius blip
        },
        {
            name = "Gang Territory - Grove Street",
            coords = vector3(-110.0, -1625.0, 32.0),
            radius = 200.0,
            color = 25,
            alpha = 80,
            enabled = true
        },
        {
            name = "No Fly Zone - Military Base",
            coords = vector3(-2360.0, 3249.0, 32.0),
            radius = 500.0,
            color = 1,
            alpha = 60,
            enabled = true
        },
        {
            name = "Racing Area - Airport",
            coords = vector3(-1336.0, -3044.0, 13.9),
            radius = 300.0,
            color = 5,
            alpha = 90,
            enabled = true
        }
    }
}

return Config
