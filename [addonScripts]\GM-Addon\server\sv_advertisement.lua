ESX = exports["es_extended"]:getSharedObject()

local Config = require('config')
local playerCooldowns = {}

local function hasAllowedJob(xPlayer)
    if not Config.Advertisement.jobRestricted then
        return true
    end

    local playerJob = xPlayer.getJob().name
    for _, allowedJob in ipairs(Config.Advertisement.allowedJobs) do
        if playerJob == allowedJob then
            return true
        end
    end
    return false
end

local function formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60

    if minutes > 0 then
        return string.format("%dm %ds", minutes, remainingSeconds)
    else
        return string.format("%ds", remainingSeconds)
    end
end

local function getPlayerBankMoney(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return 0 end

    return xPlayer.getAccount('bank').money
end

local function removePlayerBankMoney(source, amount)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end

    local bankMoney = xPlayer.getAccount('bank').money
    if bankMoney >= amount then
        xPlayer.removeAccountMoney('bank', amount)
        return true
    end
    return false
end

local function sendAdvertisementNotification(message, playerName)
    if not Config.Advertisement.notification.enabled then return end

    TriggerClientEvent('gmaddon:advertisement:show', -1, {
        title = Config.Advertisement.notification.title,
        message = message,
        author = playerName,
        duration = Config.Advertisement.notification.duration
    })
end

local function validateMessage(message)
    if not message or message == "" then
        return false, "invalid_message"
    end

    local trimmedMessage = string.gsub(message, "^%s*(.-)%s*$", "%1")
    local messageLength = string.len(trimmedMessage)

    if messageLength < Config.Advertisement.minLength then
        return false, "too_short"
    end

    if messageLength > Config.Advertisement.maxLength then
        return false, "too_long"
    end

    return true, trimmedMessage
end

RegisterCommand('ad', function(source, _, rawCommand)
    if not Config.Advertisement.enabled then
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not hasAllowedJob(xPlayer) then
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = Config.Advertisement.notifications.job_restricted
        })
        return
    end

    local playerId = tostring(source)
    local playerName = xPlayer.getName()

    if playerCooldowns[playerId] and playerCooldowns[playerId] > os.time() then
        local remainingTime = playerCooldowns[playerId] - os.time()
        local cooldownMessage = string.format(Config.Advertisement.notifications.cooldown, formatTime(remainingTime))
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = cooldownMessage
        })
        return
    end

    local adMessage = string.sub(rawCommand, 4)

    local isValid, result = validateMessage(adMessage)
    if not isValid then
        local notificationKey = result
        local notificationMessage = Config.Advertisement.notifications[notificationKey]

        if notificationKey == "too_short" then
            notificationMessage = string.format(notificationMessage, Config.Advertisement.minLength)
        elseif notificationKey == "too_long" then
            notificationMessage = string.format(notificationMessage, Config.Advertisement.maxLength)
        end

        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = notificationMessage
        })
        return
    end

    local validMessage = result

    local bankMoney = getPlayerBankMoney(source)
    if bankMoney < Config.Advertisement.cost then
        local insufficientFundsMessage = string.format(Config.Advertisement.notifications.insufficient_funds, Config.Advertisement.cost)
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = insufficientFundsMessage
        })
        return
    end

    if not removePlayerBankMoney(source, Config.Advertisement.cost) then
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = "Failed to process payment. Please try again."
        })
        return
    end

    playerCooldowns[playerId] = os.time() + Config.Advertisement.cooldown

    sendAdvertisementNotification(validMessage, playerName)

    local successMessage = string.format(Config.Advertisement.notifications.success, Config.Advertisement.cost)
    TriggerClientEvent('ox_lib:notify', source, {
        type = 'success',
        description = successMessage
    })
end, false)


CreateThread(function()
    while true do
        Wait(300000)

        local currentTime = os.time()
        for playerId, cooldownTime in pairs(playerCooldowns) do
            if cooldownTime <= currentTime then
                playerCooldowns[playerId] = nil
            end
        end
    end
end)

AddEventHandler('playerDropped', function()
    local playerId = tostring(source)
    if playerCooldowns[playerId] then
        playerCooldowns[playerId] = nil
    end
end)
