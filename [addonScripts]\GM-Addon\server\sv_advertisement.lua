ESX = exports["es_extended"]:getSharedObject()

local Config = require('config')
local playerCooldowns = {} -- Store player cooldowns
local activeNotifications = {} -- Track active notifications per player for optimization
local notificationQueue = {} -- Queue for batched notifications

-- Function to check if player has allowed job
local function hasAllowedJob(xPlayer)
    if not Config.Advertisement.jobRestricted then
        return true -- No job restrictions
    end

    local playerJob = xPlayer.getJob().name
    for _, allowedJob in ipairs(Config.Advertisement.allowedJobs) do
        if playerJob == allowedJob then
            return true
        end
    end
    return false
end

-- Function to format time remaining
local function formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60

    if minutes > 0 then
        return string.format("%dm %ds", minutes, remainingSeconds)
    else
        return string.format("%ds", remainingSeconds)
    end
end

local function getPlayerBankMoney(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return 0 end

    return xPlayer.getAccount('bank').money
end

local function removePlayerBankMoney(source, amount)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end

    local bankMoney = xPlayer.getAccount('bank').money
    if bankMoney >= amount then
        xPlayer.removeAccountMoney('bank', amount)
        return true
    end
    return false
end

-- Optimized notification function for high player count servers
local function sendAdvertisementNotification(message, playerName)
    if not Config.Advertisement.notification.enabled then return end

    local notificationData = {
        title = Config.Advertisement.notification.title,
        message = message,
        author = playerName,
        duration = Config.Advertisement.notification.duration
    }

    -- Get all players
    local players = ESX.GetPlayers()
    local totalPlayers = #players

    if Config.Advertisement.optimization.batchNotifications and totalPlayers > Config.Advertisement.optimization.batchSize then
        -- Send notifications in batches for better performance
        local batchSize = Config.Advertisement.optimization.batchSize
        local batchDelay = Config.Advertisement.optimization.batchDelay

        for i = 1, totalPlayers, batchSize do
            local batch = {}
            for j = i, math.min(i + batchSize - 1, totalPlayers) do
                table.insert(batch, players[j])
            end

            -- Send to batch with delay
            CreateThread(function()
                if i > 1 then
                    Wait(batchDelay * ((i - 1) / batchSize))
                end

                for _, playerId in ipairs(batch) do
                    -- Track active notifications per player
                    if not activeNotifications[playerId] then
                        activeNotifications[playerId] = 0
                    end

                    -- Limit concurrent notifications per player
                    if activeNotifications[playerId] < Config.Advertisement.optimization.maxConcurrentAds then
                        TriggerClientEvent('gmaddon:advertisement:show', playerId, notificationData)
                        activeNotifications[playerId] = activeNotifications[playerId] + 1

                        -- Decrease counter after notification duration
                        SetTimeout(notificationData.duration + 1000, function()
                            if activeNotifications[playerId] then
                                activeNotifications[playerId] = math.max(0, activeNotifications[playerId] - 1)
                            end
                        end)
                    end
                end
            end)
        end
    else
        -- Send to all players at once (for smaller servers)
        TriggerClientEvent('gmaddon:advertisement:show', -1, notificationData)
    end

    -- Optional chat backup (disabled by default for performance)
    if Config.Advertisement.notification.chat_backup then
        TriggerClientEvent('chat:addMessage', -1, {
            color = { 255, 165, 0 },
            multiline = true,
            args = { Config.Advertisement.notification.title, string.format("%s: %s", playerName, message) }
        })
    end

    -- Log the advertisement
    print(string.format("[ADVERTISEMENT] %s: %s (sent to %d players)", playerName, message, totalPlayers))
end

local function validateMessage(message)
    if not message or message == "" then
        return false, "invalid_message"
    end

    local trimmedMessage = string.gsub(message, "^%s*(.-)%s*$", "%1")
    local messageLength = string.len(trimmedMessage)

    if messageLength < Config.Advertisement.minLength then
        return false, "too_short"
    end

    if messageLength > Config.Advertisement.maxLength then
        return false, "too_long"
    end

    return true, trimmedMessage
end

RegisterCommand('ad', function(source, _, rawCommand)
    if not Config.Advertisement.enabled then
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    -- Check job restrictions
    if not hasAllowedJob(xPlayer) then
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = Config.Advertisement.notifications.job_restricted
        })
        return
    end

    local playerId = tostring(source)
    local playerName = xPlayer.getName()

    if playerCooldowns[playerId] and playerCooldowns[playerId] > os.time() then
        local remainingTime = playerCooldowns[playerId] - os.time()
        local cooldownMessage = string.format(Config.Advertisement.notifications.cooldown, formatTime(remainingTime))
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = cooldownMessage
        })
        return
    end

    local adMessage = string.sub(rawCommand, 4)

    local isValid, result = validateMessage(adMessage)
    if not isValid then
        local notificationKey = result
        local notificationMessage = Config.Advertisement.notifications[notificationKey]

        if notificationKey == "too_short" then
            notificationMessage = string.format(notificationMessage, Config.Advertisement.minLength)
        elseif notificationKey == "too_long" then
            notificationMessage = string.format(notificationMessage, Config.Advertisement.maxLength)
        end

        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = notificationMessage
        })
        return
    end

    local validMessage = result

    local bankMoney = getPlayerBankMoney(source)
    if bankMoney < Config.Advertisement.cost then
        local insufficientFundsMessage = string.format(Config.Advertisement.notifications.insufficient_funds, Config.Advertisement.cost)
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = insufficientFundsMessage
        })
        return
    end

    if not removePlayerBankMoney(source, Config.Advertisement.cost) then
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = "Failed to process payment. Please try again."
        })
        return
    end

    playerCooldowns[playerId] = os.time() + Config.Advertisement.cooldown

    sendAdvertisementNotification(validMessage, playerName)

    local successMessage = string.format(Config.Advertisement.notifications.success, Config.Advertisement.cost)
    TriggerClientEvent('ox_lib:notify', source, {
        type = 'success',
        description = successMessage
    })
end, false)

-- Optimized cleanup system for high player count servers
CreateThread(function()
    while true do
        Wait(Config.Advertisement.optimization.cleanupInterval * 1000) -- Use configurable cleanup interval

        local currentTime = os.time()
        local cleanedCooldowns = 0
        local cleanedNotifications = 0

        -- Clean up expired cooldowns
        for playerId, cooldownTime in pairs(playerCooldowns) do
            if cooldownTime <= currentTime then
                playerCooldowns[playerId] = nil
                cleanedCooldowns = cleanedCooldowns + 1
            end
        end

        -- Clean up notification counters for offline players
        local onlinePlayers = {}
        for _, playerId in ipairs(ESX.GetPlayers()) do
            onlinePlayers[tostring(playerId)] = true
        end

        for playerId, _ in pairs(activeNotifications) do
            if not onlinePlayers[playerId] then
                activeNotifications[playerId] = nil
                cleanedNotifications = cleanedNotifications + 1
            end
        end

        -- Log cleanup if significant
        if cleanedCooldowns > 0 or cleanedNotifications > 0 then
            print(string.format("[ADVERTISEMENT] Cleanup: %d cooldowns, %d notifications", cleanedCooldowns, cleanedNotifications))
        end
    end
end)

-- Clean up player data when they disconnect
AddEventHandler('playerDropped', function()
    local playerId = tostring(source)

    -- Clean up cooldowns
    if playerCooldowns[playerId] then
        playerCooldowns[playerId] = nil
    end

    -- Clean up notification counters
    if activeNotifications[playerId] then
        activeNotifications[playerId] = nil
    end
end)
