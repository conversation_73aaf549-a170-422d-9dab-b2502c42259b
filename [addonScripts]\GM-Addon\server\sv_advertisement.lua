ESX = exports["es_extended"]:getSharedObject()

local Config = require('config')
local playerCooldowns = {} -- Store player cooldowns

-- Function to format time remaining
local function formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    
    if minutes > 0 then
        return string.format("%dm %ds", minutes, remainingSeconds)
    else
        return string.format("%ds", remainingSeconds)
    end
end

-- Function to get player's bank money
local function getPlayerBankMoney(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return 0 end
    
    return xPlayer.getAccount('bank').money
end

-- Function to remove money from player's bank
local function removePlayerBankMoney(source, amount)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end
    
    local bankMoney = xPlayer.getAccount('bank').money
    if bankMoney >= amount then
        xPlayer.removeAccountMoney('bank', amount)
        return true
    end
    return false
end

-- Function to send TXAdmin notification
local function sendTXAdminNotification(message, playerName)
    if not Config.Advertisement.txadmin.enabled then return end

    local formattedMessage = string.format("%s: %s", playerName, message)

    -- Method 1: Try TXAdmin's built-in announcement command
    local txAdminSuccess = false

    -- Try different TXAdmin announcement methods
    local txAdminMethods = {
        function() ExecuteCommand(string.format('announce %s', formattedMessage)) end,
        function() ExecuteCommand(string.format('tx:announce %s', formattedMessage)) end,
        function()
            if GetResourceState('monitor') == 'started' then
                exports.monitor:announce(Config.Advertisement.txadmin.title, formattedMessage)
            end
        end
    }

    for _, method in ipairs(txAdminMethods) do
        local success = pcall(method)
        if success then
            txAdminSuccess = true
            break
        end
    end

    -- Fallback methods if TXAdmin doesn't work
    if not txAdminSuccess then
        -- Method 2: Chat announcement for all players (if enabled)
        if Config.Advertisement.txadmin.fallback_to_chat then
            TriggerClientEvent('chat:addMessage', -1, {
                color = { 255, 165, 0 }, -- Orange color for advertisements
                multiline = true,
                args = { Config.Advertisement.txadmin.title, formattedMessage }
            })
        end

        -- Method 3: ox_lib notification for all players (if enabled)
        if Config.Advertisement.txadmin.fallback_to_notify then
            TriggerClientEvent('ox_lib:notify', -1, {
                type = Config.Advertisement.txadmin.type,
                title = Config.Advertisement.txadmin.title,
                description = formattedMessage,
                duration = Config.Advertisement.txadmin.duration,
                position = 'top'
            })
        end
    end

    -- Log the method used
    if txAdminSuccess then
        print(string.format("[ADVERTISEMENT] Sent via TXAdmin: %s", formattedMessage))
    else
        print(string.format("[ADVERTISEMENT] Sent via fallback methods: %s", formattedMessage))
    end
end

-- Function to validate advertisement message
local function validateMessage(message)
    if not message or message == "" then
        return false, "invalid_message"
    end
    
    local trimmedMessage = string.gsub(message, "^%s*(.-)%s*$", "%1") -- Trim whitespace
    local messageLength = string.len(trimmedMessage)
    
    if messageLength < Config.Advertisement.minLength then
        return false, "too_short"
    end
    
    if messageLength > Config.Advertisement.maxLength then
        return false, "too_long"
    end
    
    return true, trimmedMessage
end

-- Register the /ad command
RegisterCommand('ad', function(source, args, rawCommand)
    -- Check if advertisement system is enabled
    if not Config.Advertisement.enabled then
        return
    end
    
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    local playerId = tostring(source)
    local playerName = xPlayer.getName()
    
    -- Check cooldown
    if playerCooldowns[playerId] and playerCooldowns[playerId] > os.time() then
        local remainingTime = playerCooldowns[playerId] - os.time()
        local message = string.format(Config.Advertisement.notifications.cooldown, formatTime(remainingTime))
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = message
        })
        return
    end
    
    -- Get the advertisement message (everything after /ad)
    local message = string.sub(rawCommand, 4) -- Remove "/ad " from the beginning
    
    -- Validate the message
    local isValid, result = validateMessage(message)
    if not isValid then
        local notificationKey = result
        local notificationMessage = Config.Advertisement.notifications[notificationKey]
        
        if notificationKey == "too_short" then
            notificationMessage = string.format(notificationMessage, Config.Advertisement.minLength)
        elseif notificationKey == "too_long" then
            notificationMessage = string.format(notificationMessage, Config.Advertisement.maxLength)
        end
        
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = notificationMessage
        })
        return
    end
    
    local validMessage = result
    
    -- Check if player has enough money
    local bankMoney = getPlayerBankMoney(source)
    if bankMoney < Config.Advertisement.cost then
        local message = string.format(Config.Advertisement.notifications.insufficient_funds, Config.Advertisement.cost)
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = message
        })
        return
    end
    
    -- Remove money from player's bank
    if not removePlayerBankMoney(source, Config.Advertisement.cost) then
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = "Failed to process payment. Please try again."
        })
        return
    end
    
    -- Set cooldown
    playerCooldowns[playerId] = os.time() + Config.Advertisement.cooldown
    
    -- Send TXAdmin notification
    sendTXAdminNotification(validMessage, playerName)
    
    -- Notify the player of success
    local successMessage = string.format(Config.Advertisement.notifications.success, Config.Advertisement.cost)
    TriggerClientEvent('ox_lib:notify', source, {
        type = 'success',
        description = successMessage
    })
    
    -- Log the advertisement for server console
    print(string.format("[ADVERTISEMENT] %s (%s): %s", playerName, source, validMessage))
    
end, false) -- false means the command is available to all players

-- Clean up cooldowns periodically to prevent memory leaks
CreateThread(function()
    while true do
        Wait(300000) -- Check every 5 minutes
        
        local currentTime = os.time()
        for playerId, cooldownTime in pairs(playerCooldowns) do
            if cooldownTime <= currentTime then
                playerCooldowns[playerId] = nil
            end
        end
    end
end)

-- Clear cooldown when player disconnects
AddEventHandler('playerDropped', function(reason)
    local playerId = tostring(source)
    if playerCooldowns[playerId] then
        playerCooldowns[playerId] = nil
    end
end)
