local Config = {
    blacklisted = {
        -- Vehicles
        [`SHAMAL`] = true,
        [`LUXOR`] = true,
        [`LUXOR2`] = true,
        [`JET`] = true,
        [`LAZER`] = true,
        [`BUZZARD`] = true,
        [`BUZZARD2`] = true,
        [`ANNI<PERSON><PERSON><PERSON>OR`] = true,
        [`<PERSON>VAGE`] = true,
        [`TITAN`] = true,
        [`RHINO`] = true,
        [`HYDRA`] = true,
        [`OPPRESSOR`] = true,
        [`OPPRESSOR2`] = true,
        [`DELUXO`] = true,
        [`RUINER2`] = true,
        [`SCRAMJET`] = true,
        [`VIGILANTE`] = true,
        [`<PERSON>HA<PERSON><PERSON><PERSON>I`] = true,
        [`APC`] = true,
        [`HALFTRACK`] = true,
        [`HUNTER`] = true,
        [`AKULA`] = true,
        [`STRIKEFORCE`] = true,
        
        -- Police Vehicles
        [`POLICE`] = true,
        [`POLICE2`] = true,
        [`POLICE3`] = true,
        [`POLICE4`] = true,
        [`POLICEB`] = true,
        [`POLICEOLD1`] = true,
        [`POLICEOLD2`] = true,
        [`POLICET`] = true,
        [`SHERIFF`] = true,
        [`SHERIFF2`] = true,
        
        -- Emergency Vehicles
        [`FIRETRUK`] = true,
        
        -- Peds
        [`s_m_y_ranger_01`] = true,
        [`s_m_y_sheriff_01`] = true,
        [`s_m_y_cop_01`] = true,
        [`s_f_y_sheriff_01`] = true,
        [`s_f_y_cop_01`] = true,
        [`s_m_y_hwaycop_01`] = true,
        [`s_m_y_fireman_01`] = true,
        
        -- Trains
        [`freight`] = true,
        [`freightcar`] = true,
        [`freightcar2`] = true,
        [`metrotrain`] = true,
        [`tram`] = true,
    }
}

if not Config.blacklisted or next(Config.blacklisted) == nil then
    return
end

local blacklistedHashes = {}
for key, _ in pairs(Config.blacklisted) do
    if type(key) == "number" then
        blacklistedHashes[key] = true
    elseif type(key) == "string" then
        blacklistedHashes[GetHashKey(key)] = true
    end
end

AddEventHandler('entityCreating', function(entity)
    local entityType = GetEntityType(entity)
    
    if entityType ~= 1 and entityType ~= 2 then
        return
    end
    
    local model = GetEntityModel(entity)
    if blacklistedHashes[model] then
        CancelEvent()
    end
end)

--[[
CreateThread(function()
    while true do
        Wait(30000) -- Check every 2 minutes (reduced frequency)
        
        local entities = GetGamePool('CVehicle')
        for i = 1, #entities do
            local entity = entities[i]
            if DoesEntityExist(entity) then
                local model = GetEntityModel(entity)
                if blacklistedHashes[model] then
                    DeleteEntity(entity)
                end
            end
        end
        
        local peds = GetGamePool('CPed')
        for i = 1, #peds do
            local ped = peds[i]
            if DoesEntityExist(ped) and not IsPedAPlayer(ped) then
                local model = GetEntityModel(ped)
                if blacklistedHashes[model] then
                    DeleteEntity(ped)
                end
            end
        end
    end
end)
--]]