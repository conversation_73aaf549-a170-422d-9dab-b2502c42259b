local Config = require('config')

TriggerEvent('chat:addSuggestion', '/ad', 'Post a city-wide advertisement', {
    { name = "message", help = string.format("Your advertisement message (min: %d, max: %d characters)", Config.Advertisement.minLength, Config.Advertisement.maxLength) }
})

RegisterNetEvent('gmaddon:advertisement:show', function(data)
    SendNUIMessage({
        type = 'showAdvertisement',
        title = data.title or 'City Advertisement',
        message = data.message,
        author = data.author,
        duration = data.duration or 15000
    })

    PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", true)
end)