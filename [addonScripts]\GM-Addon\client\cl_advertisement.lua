-- Client-side advertisement system
-- This file handles client-side functionality for the advertisement system

local Config = require('config')
local isNuiReady = false

-- Register chat suggestion for the /ad command
TriggerEvent('chat:addSuggestion', '/ad', 'Post a city-wide advertisement', {
    { name = "message", help = string.format("Your advertisement message (min: %d, max: %d characters)", Config.Advertisement.minLength, Config.Advertisement.maxLength) }
})

-- Initialize NUI
CreateThread(function()
    Wait(1000) -- Wait for everything to load
    isNuiReady = true
end)

-- Handle custom advertisement notifications
RegisterNetEvent('gmaddon:advertisement:show', function(data)
    if not isNuiReady then
        Wait(1000)
    end

    -- Send data to NUI
    SendNUIMessage({
        type = 'showAdvertisement',
        title = data.title or 'City Advertisement',
        message = data.message,
        author = data.author,
        duration = data.duration or 8000
    })

    -- Play notification sound
    PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", 1)
end)

-- Optional: Add a keybind for quick access to advertisement command
-- Uncomment the lines below if you want to add a keybind
--[[
RegisterKeyMapping('openAdMenu', 'Open Advertisement Menu', 'keyboard', 'F7')

RegisterCommand('openAdMenu', function()
    -- Create a simple input dialog for the advertisement
    local input = lib.inputDialog('City Advertisement', {
        {
            type = 'textarea',
            label = 'Advertisement Message',
            description = string.format('Enter your advertisement message (Cost: $%d)', Config.Advertisement.cost),
            placeholder = 'Enter your message here...',
            required = true,
            min = Config.Advertisement.minLength,
            max = Config.Advertisement.maxLength
        }
    })

    if input and input[1] then
        local message = input[1]
        ExecuteCommand('ad ' .. message)
    end
end, false)
--]]

-- Optional: Show advertisement cost in chat when typing /ad
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end

    -- Show information about the advertisement system
    if Config.Advertisement.enabled then
        TriggerEvent('chat:addMessage', {
            color = { 0, 255, 0 },
            multiline = true,
            args = { "Advertisement System", string.format("Use /ad [message] to post city-wide advertisements. Cost: $%d, Cooldown: %d seconds", Config.Advertisement.cost, Config.Advertisement.cooldown) }
        })
    end
end)

-- Debug command to test notifications (remove in production)
RegisterCommand('testadnotif', function()
    TriggerEvent('gmaddon:advertisement:show', {
        title = 'Test Advertisement',
        message = 'This is a test advertisement to check if the custom UI is working properly!',
        author = 'Test Player',
        duration = 5000
    })
end, false)
