local Config = require('config')
local isNuiReady = false

TriggerEvent('chat:addSuggestion', '/ad', 'Post a city-wide advertisement', {
    { name = "message", help = string.format("Your advertisement message (min: %d, max: %d characters)", Config.Advertisement.minLength, Config.Advertisement.maxLength) }
})

CreateThread(function()
    Wait(1000) 
    isNuiReady = true
end)


RegisterNetEvent('gmaddon:advertisement:show', function(data)
    if not isNuiReady then
        Wait(1000)
    end

    SendNUIMessage({
        type = 'showAdvertisement',
        title = data.title or 'City Advertisement',
        message = data.message,
        author = data.author,
        duration = data.duration or 15000
    })

    PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", true)
end)