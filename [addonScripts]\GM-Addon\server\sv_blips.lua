local Config = require('config')

RegisterCommand('refreshblips_all', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    if xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'superadmin' then
        TriggerClientEvent('gmaddon:blips:refresh', -1)
        TriggerClientEvent('chat:addMessage', -1, {
            color = { 0, 255, 0 },
            args = { "[ADMIN]", "All player blips have been refreshed by an administrator." }
        })
        print(string.format("[BLIPS] %s refreshed all player blips", xPlayer.getName()))
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = { 255, 0, 0 },
            args = { "[ERROR]", "You don't have permission to use this command." }
        })
    end
end, false)

RegisterCommand('toggleblips_all', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end
    
    if xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'superadmin' then
        local action = args[1]
        if action == 'on' then
            TriggerClientEvent('gmaddon:blips:toggle', -1, true)
            TriggerClientEvent('chat:addMessage', -1, {
                color = { 0, 255, 0 },
                args = { "[ADMIN]", "Blips have been enabled for all players by an administrator." }
            })
            print(string.format("[BLIPS] %s enabled blips for all players", xPlayer.getName()))
        elseif action == 'off' then
            TriggerClientEvent('gmaddon:blips:toggle', -1, false)
            TriggerClientEvent('chat:addMessage', -1, {
                color = { 255, 165, 0 },
                args = { "[ADMIN]", "Blips have been disabled for all players by an administrator." }
            })
            print(string.format("[BLIPS] %s disabled blips for all players", xPlayer.getName()))
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = { 255, 255, 0 },
                args = { "[USAGE]", "Usage: /toggleblips_all [on/off]" }
            })
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = { 255, 0, 0 },
            args = { "[ERROR]", "You don't have permission to use this command." }
        })
    end
end, false)

AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    if Config.Blips.enabled then
        print("[BLIPS] Server-side blip management loaded successfully")
        print(string.format("[BLIPS] Configured %d regular blips and %d radius blips", 
            #Config.Blips.regularBlips, #Config.Blips.radiusBlips))
    else
        print("[BLIPS] Blip system is disabled in config")
    end
end)
